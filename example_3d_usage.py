#!/usr/bin/env python3
"""
3D控制系统使用示例
演示如何使用新的三维速度控制功能
"""
import numpy as np
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.teacher_model import TeacherModel
from models.student_model import StudentModel
from training.data_collector import DataCollector
from training.trainer import Trainer
from config import *

def example_teacher_3d_control():
    """示例：教师模型3D控制"""
    print("=== 教师模型3D控制示例 ===")
    
    teacher = TeacherModel()
    
    # 模拟不同场景的深度图像
    scenarios = [
        ("正常飞行", np.random.rand(DEPTH_HEIGHT, DEPTH_WIDTH) * 20 + 10, 2.0),
        ("前方有障碍", np.concatenate([
            np.ones((DEPTH_HEIGHT, DEPTH_WIDTH//2)) * 3,  # 左侧近距离障碍
            np.ones((DEPTH_HEIGHT, DEPTH_WIDTH//2)) * 15   # 右侧远距离
        ], axis=1), 3.0),
        ("高度过高", np.random.rand(DEPTH_HEIGHT, DEPTH_WIDTH) * 15 + 5, 6.0),  # 超过警戒高度
    ]
    
    for scenario_name, depth_image, height in scenarios:
        print(f"\n场景: {scenario_name}")
        print(f"当前高度: {height}m")
        
        velocity_vector = teacher.get_velocity_vector(depth_image, height)
        
        print(f"输出速度向量: Vx={velocity_vector[0]:.2f}, Vy={velocity_vector[1]:.2f}, Vz={velocity_vector[2]:.2f}")
        print(f"水平速度大小: {np.linalg.norm(velocity_vector[:2]):.2f}m/s")
        
        # 分析飞行策略
        if abs(velocity_vector[1]) > 0.5:
            direction = "左转" if velocity_vector[1] < 0 else "右转"
            print(f"飞行策略: {direction}")
        else:
            print("飞行策略: 直行")

def example_student_3d_control():
    """示例：学生模型3D控制"""
    print("\n=== 学生模型3D控制示例 ===")
    
    student = StudentModel()
    
    # 模拟输入
    depth_image = np.random.rand(DEPTH_HEIGHT, DEPTH_WIDTH).astype(np.float32)
    height = 4.0
    
    print(f"输入深度图像形状: {depth_image.shape}")
    print(f"输入高度: {height}m")
    
    velocity_vector = student.predict(depth_image, height)
    
    print(f"预测速度向量: Vx={velocity_vector[0]:.3f}, Vy={velocity_vector[1]:.3f}, Vz={velocity_vector[2]:.3f}")
    print(f"水平速度大小: {np.linalg.norm(velocity_vector[:2]):.3f}m/s")

def example_data_collection():
    """示例：3D数据收集"""
    print("\n=== 3D数据收集示例 ===")
    
    # 注意：这个示例不会实际连接AirSim，只是演示数据格式
    print("数据收集格式说明：")
    print("- 深度图像: [N, height, width]")
    print("- 高度数据: [N]")
    print("- 动作数据: [N, 3] (Vx, Vy, Vz)")
    
    # 模拟收集的数据
    num_samples = 100
    depth_images = np.random.rand(num_samples, DEPTH_HEIGHT, DEPTH_WIDTH)
    heights = np.random.rand(num_samples) * 4 + 1  # 1-5米高度
    
    # 使用教师模型生成对应的动作
    teacher = TeacherModel()
    actions = []
    
    print(f"生成 {num_samples} 个样本的动作数据...")
    for i in range(num_samples):
        if i % 20 == 0:
            print(f"进度: {i}/{num_samples}")
        
        velocity = teacher.get_velocity_vector(depth_images[i], heights[i])
        actions.append(velocity)
    
    actions = np.array(actions)
    
    print(f"收集完成！")
    print(f"深度图像形状: {depth_images.shape}")
    print(f"高度形状: {heights.shape}")
    print(f"动作形状: {actions.shape}")
    
    # 数据统计
    print(f"\n数据统计:")
    print(f"高度范围: {np.min(heights):.2f} - {np.max(heights):.2f}m")
    print(f"Vx范围: {np.min(actions[:, 0]):.2f} - {np.max(actions[:, 0]):.2f}")
    print(f"Vy范围: {np.min(actions[:, 1]):.2f} - {np.max(actions[:, 1]):.2f}")
    print(f"Vz范围: {np.min(actions[:, 2]):.2f} - {np.max(actions[:, 2]):.2f}")
    
    return depth_images, heights, actions

def example_training():
    """示例：3D模型训练"""
    print("\n=== 3D模型训练示例 ===")
    
    # 生成训练数据
    print("生成训练数据...")
    depth_images, heights, actions = example_data_collection()
    
    # 创建学生模型和训练器
    student = StudentModel()
    trainer = Trainer(student)
    
    print("开始训练...")
    # 使用少量epoch进行演示
    loss_history = trainer.train(depth_images, heights, actions, epochs=5, batch_size=16)
    
    print(f"训练完成！最终损失: {loss_history[-1]:.6f}")
    
    # 评估模型
    print("评估模型...")
    mae = trainer.evaluate(depth_images[:20], heights[:20], actions[:20])  # 使用少量数据评估
    print(f"平均绝对误差: {mae:.6f}")

def example_backward_compatibility():
    """示例：向后兼容性"""
    print("\n=== 向后兼容性示例 ===")
    
    # 模拟旧格式数据（转向角）
    old_actions = np.random.rand(10) * 60 - 30  # -30到30度的转向角
    
    print("旧格式动作（转向角）:")
    print(old_actions[:5])
    
    # 转换为新格式（3D速度向量）
    new_actions = []
    for angle in old_actions:
        angle_rad = np.deg2rad(angle)
        vx = FORWARD_SPEED * np.cos(angle_rad)
        vy = FORWARD_SPEED * np.sin(angle_rad)
        vz = 0.0
        new_actions.append([vx, vy, vz])
    
    new_actions = np.array(new_actions)
    
    print("\n转换后的3D速度向量:")
    print(new_actions[:5])
    
    print(f"\n验证总速度大小保持为 {FORWARD_SPEED}m/s:")
    for i in range(5):
        speed = np.linalg.norm(new_actions[i, :2])
        print(f"样本 {i}: {speed:.3f}m/s")

def main():
    """主函数"""
    print("🚁 3D控制系统使用示例")
    print("="*50)
    
    examples = [
        ("教师模型3D控制", example_teacher_3d_control),
        ("学生模型3D控制", example_student_3d_control),
        ("3D数据收集", example_data_collection),
        ("向后兼容性", example_backward_compatibility),
    ]
    
    for example_name, example_func in examples:
        print(f"\n{'='*60}")
        print(f"运行示例: {example_name}")
        print('='*60)
        
        try:
            if example_name == "3D数据收集":
                # 这个示例返回数据，我们需要接收
                example_func()
            else:
                example_func()
            print(f"✓ {example_name} 示例运行成功")
        except Exception as e:
            print(f"✗ {example_name} 示例运行失败: {e}")
    
    print(f"\n{'='*60}")
    print("🎉 所有示例运行完成！")
    print("现在您可以开始使用3D控制系统了。")
    print("="*60)

if __name__ == "__main__":
    main()
