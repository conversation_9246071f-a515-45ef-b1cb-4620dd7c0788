#!/usr/bin/env python3
"""
测试3D控制系统
"""
import numpy as np
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.teacher_model import TeacherModel
from models.student_model import StudentModel
from config import *

def test_teacher_model():
    """测试教师模型的3D速度输出"""
    print("=== 测试教师模型 ===")
    
    teacher = TeacherModel()
    
    # 创建模拟深度图像
    depth_image = np.random.rand(DEPTH_HEIGHT, DEPTH_WIDTH) * 10 + 5  # 5-15米的深度
    current_height = 3.0  # 当前高度3米
    
    print(f"输入深度图像形状: {depth_image.shape}")
    print(f"当前高度: {current_height}m")
    
    # 测试get_velocity_vector方法
    try:
        velocity_vector = teacher.get_velocity_vector(depth_image, current_height)
        print(f"输出速度向量: {velocity_vector}")
        print(f"速度向量形状: {velocity_vector.shape}")
        print(f"水平速度大小: {np.linalg.norm(velocity_vector[:2]):.2f}m/s")
        print("✓ 教师模型测试通过")
    except Exception as e:
        print(f"✗ 教师模型测试失败: {e}")
        return False
    
    return True

def test_student_model():
    """测试学生模型的3D速度输出"""
    print("\n=== 测试学生模型 ===")
    
    try:
        student = StudentModel()
        
        # 创建模拟输入
        depth_image = np.random.rand(DEPTH_HEIGHT, DEPTH_WIDTH).astype(np.float32)
        height = 3.0
        
        print(f"输入深度图像形状: {depth_image.shape}")
        print(f"输入高度: {height}m")
        
        # 测试predict方法
        velocity_vector = student.predict(depth_image, height)
        print(f"输出速度向量: {velocity_vector}")
        print(f"速度向量形状: {velocity_vector.shape}")
        print("✓ 学生模型测试通过")
        
    except Exception as e:
        print(f"✗ 学生模型测试失败: {e}")
        return False
    
    return True

def test_data_format():
    """测试数据格式"""
    print("\n=== 测试数据格式 ===")
    
    try:
        from training.data_collector import DataCollector
        from models.teacher_model import TeacherModel
        
        # 创建模拟数据
        num_samples = 10
        depth_images = np.random.rand(num_samples, DEPTH_HEIGHT, DEPTH_WIDTH)
        heights = np.random.rand(num_samples) * 5 + 1  # 1-6米高度
        actions = np.random.rand(num_samples, 3) * 2 - 1  # -1到1的速度
        
        print(f"深度图像形状: {depth_images.shape}")
        print(f"高度形状: {heights.shape}")
        print(f"动作形状: {actions.shape}")
        
        # 测试数据集
        from models.student_model import DepthDataset
        dataset = DepthDataset(depth_images, heights, actions)
        
        print(f"数据集长度: {len(dataset)}")
        
        # 测试数据加载
        depth_img, height, action = dataset[0]
        print(f"单个样本 - 深度图像: {depth_img.shape}, 高度: {height.shape}, 动作: {action.shape}")
        print("✓ 数据格式测试通过")
        
    except Exception as e:
        print(f"✗ 数据格式测试失败: {e}")
        return False
    
    return True

def test_config():
    """测试配置"""
    print("\n=== 测试配置 ===")
    
    print(f"输出维度: {OUTPUT_SIZE} (应该是3)")
    print(f"高度输入维度: {HEIGHT_INPUT_SIZE} (应该是1)")
    print(f"高度警戒阈值: {HEIGHT_WARNING_THRESHOLD}m")
    print(f"高度限制比例: {HEIGHT_RESTRICTION_RATIO}")
    
    if OUTPUT_SIZE == 3 and HEIGHT_INPUT_SIZE == 1:
        print("✓ 配置测试通过")
        return True
    else:
        print("✗ 配置测试失败")
        return False

def main():
    """主测试函数"""
    print("开始测试3D控制系统...")
    
    tests = [
        ("配置", test_config),
        ("教师模型", test_teacher_model),
        ("学生模型", test_student_model),
        ("数据格式", test_data_format),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 测试通过")
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试出错: {e}")
    
    print(f"\n{'='*50}")
    print(f"测试结果: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有测试通过！3D控制系统准备就绪。")
        return True
    else:
        print("❌ 部分测试失败，请检查代码。")
        return False

if __name__ == "__main__":
    main()
