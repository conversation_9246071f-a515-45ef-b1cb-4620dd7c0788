"""
教师模型（规则算法）
"""
import numpy as np
import math
from typing import List, Tu<PERSON>, Dict, Any

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import (
    MIN_OBSTACLE_DISTANCE, MAX_TURN_ANGLE, DRONE_RADIUS, FORWARD_SPEED,
    CAMERA_FOV_H, CAMERA_FOV_V, DEPTH_SCALE, MAX_DEPTH, SKY_DEPTH,
    POINT_CLOUD_ANGLE_BINS, POINT_CLOUD_ANGLE_RANGE, COLLISION_DETECTION_DIRECTIONS,
    HEIGHT_WARNING_THRESHOLD, HEIGHT_RESTRICTION_RATIO, DEPTH_HEIGHT
)
from environment.depth_utils import (
    process_depth_image, depth_to_point_cloud, get_front_points,
    points_to_distance_array, detect_collision_directions, find_safe_direction
)

class TeacherModel:
    """
    基于规则的避障算法，作为教师模型
    """
    def __init__(self, min_distance: float = MIN_OBSTACLE_DISTANCE, max_angle: float = MAX_TURN_ANGLE):
        """
        初始化教师模型

        Args:
            min_distance: 最小安全距离
            max_angle: 最大转向角
        """
        self.min_distance = min_distance
        self.max_angle = max_angle

        # 缓存最近的点云和距离数组，用于可视化和调试
        self.last_depth_image = None
        self.last_point_cloud = None
        self.last_distance_array = None
        self.last_collision_risks = None
        self.current_yaw = 0.0  # 当前偏航角，用于计算相对转向角

    def apply_height_restriction(self, depth_img: np.ndarray, current_height: float) -> np.ndarray:
        """
        应用高度限制，当高度超过警戒阈值时，将深度图像上部区域设为高风险

        Args:
            depth_img: 深度图像
            current_height: 当前高度

        Returns:
            处理后的深度图像
        """
        if current_height > HEIGHT_WARNING_THRESHOLD:
            # 计算需要限制的上部区域
            restricted_height = int(DEPTH_HEIGHT * (1 - HEIGHT_RESTRICTION_RATIO))

            # 将上部区域的深度值设为最小值，表示高风险
            modified_depth = depth_img.copy()
            modified_depth[:restricted_height, :] = 0.1  # 设为很小的深度值，表示极高风险

            print(f"[高度限制] 当前高度: {current_height:.2f}m > 警戒高度: {HEIGHT_WARNING_THRESHOLD:.2f}m")
            print(f"[高度限制] 限制上部 {restricted_height} 行像素 (总共 {DEPTH_HEIGHT} 行)")

            return modified_depth

        return depth_img

    def process_depth_image(self, depth_img: np.ndarray, current_height: float = 0.0) -> Dict[str, Any]:
        """
        处理深度图像，提取点云和距离数组

        Args:
            depth_img: 深度图像
            current_height: 当前高度

        Returns:
            处理结果字典
        """
        # 应用高度限制
        height_restricted_depth = self.apply_height_restriction(depth_img, current_height)

        # 缓存深度图像
        self.last_depth_image = height_restricted_depth

        # 处理深度图像（处理无效值和天空）
        processed_depth = process_depth_image(height_restricted_depth, MAX_DEPTH, SKY_DEPTH)

        # 将深度图像转换为点云
        points = depth_to_point_cloud(processed_depth, CAMERA_FOV_H, DEPTH_SCALE)

        # 缓存点云
        self.last_point_cloud = points

        # 如果没有点，返回空结果
        if len(points) == 0:
            self.last_distance_array = np.full(POINT_CLOUD_ANGLE_BINS, MAX_DEPTH)
            self.last_collision_risks = np.zeros(COLLISION_DETECTION_DIRECTIONS)
            return {
                "distance_array": self.last_distance_array,
                "collision_risks": self.last_collision_risks,
                "min_distance": MAX_DEPTH
            }

        # 获取前方点云
        front_points = get_front_points(points, POINT_CLOUD_ANGLE_RANGE)

        # 将点云转换为距离数组
        distance_array = points_to_distance_array(
            front_points,
            POINT_CLOUD_ANGLE_BINS,
            POINT_CLOUD_ANGLE_RANGE
        )

        # 缓存距离数组
        self.last_distance_array = distance_array

        # 检测可能发生碰撞的方向
        collision_risks = detect_collision_directions(
            points,
            DRONE_RADIUS,
            self.min_distance,
            COLLISION_DETECTION_DIRECTIONS,
            CAMERA_FOV_V
        )

        # 缓存碰撞风险
        self.last_collision_risks = collision_risks

        # 计算最小距离
        min_distance = np.min(distance_array)

        return {
            "distance_array": distance_array,
            "collision_risks": collision_risks,
            "min_distance": min_distance
        }

    def get_safe_direction(self, observation: np.ndarray) -> float:
        """
        获取最安全的方向

        Args:
            observation: 深度图像或距离数组

        Returns:
            最安全方向的角度（度）
        """
        # 检查输入类型
        if len(observation.shape) == 2:
            # 输入是深度图像
            result = self.process_depth_image(observation)
            distance_array = result["distance_array"]
            collision_risks = result["collision_risks"]
            min_distance = result["min_distance"]

            # 打印调试信息
            print(f"[避障调试] 最小障碍物距离: {min_distance:.2f}m")
            print(f"[避障调试] 危险阈值: {self.min_distance:.2f}m, 警告阈值: {self.min_distance * 1.5:.2f}m")

            # 打印碰撞风险
            if collision_risks is not None and len(collision_risks) > 0:
                max_risk = np.max(collision_risks)
                max_risk_idx = np.argmax(collision_risks)
                max_risk_angle = -180 + max_risk_idx * (360.0 / len(collision_risks))
                print(f"[避障调试] 最大碰撞风险: {max_risk:.4f}, 方向: {max_risk_angle:.2f}度")

                # 打印所有方向的碰撞风险
                risk_threshold = 0.3  # 只打印风险大于此值的方向
                high_risk_directions = []
                for i, risk in enumerate(collision_risks):
                    if risk > risk_threshold:
                        angle = -180 + i * (360.0 / len(collision_risks))
                        high_risk_directions.append(f"{angle:.1f}度({risk:.2f})")

                if high_risk_directions:
                    print(f"[避障调试] 高风险方向: {', '.join(high_risk_directions)}")
                else:
                    print(f"[避障调试] 没有检测到高风险方向")
        else:
            # 输入是距离数组
            distance_array = observation
            min_distance = np.min(distance_array)
            print(f"[避障调试] 最小障碍物距离: {min_distance:.2f}m")

            # 如果没有碰撞风险数据，使用距离数组计算
            if self.last_collision_risks is None:
                # 将距离数组转换为点云（简化版）
                angles = np.linspace(-90, 90, len(distance_array)) * np.pi / 180
                x = distance_array * np.cos(angles)
                y = distance_array * np.sin(angles)
                z = np.zeros_like(x)
                points = np.column_stack((x, y, z))

                # 检测可能发生碰撞的方向
                collision_risks = detect_collision_directions(
                    points,
                    DRONE_RADIUS,
                    self.min_distance,
                    COLLISION_DETECTION_DIRECTIONS,
                    CAMERA_FOV_V
                )

                # 打印碰撞风险
                if collision_risks is not None and len(collision_risks) > 0:
                    max_risk = np.max(collision_risks)
                    max_risk_idx = np.argmax(collision_risks)
                    max_risk_angle = -180 + max_risk_idx * (360.0 / len(collision_risks))
                    print(f"[避障调试] 最大碰撞风险: {max_risk:.4f}, 方向: {max_risk_angle:.2f}度")
            else:
                collision_risks = self.last_collision_risks

        # 使用基于点云的安全方向计算
        if collision_risks is not None and len(collision_risks) > 0:
            # 使用find_safe_direction函数找到最安全的方向
            turn_angle = find_safe_direction(collision_risks, self.current_yaw, self.max_angle)
            print(f"[避障调试] 计算得到的转向角: {turn_angle:.2f}度")
            return turn_angle

        # 如果没有碰撞风险数据，使用基于距离数组的安全方向计算（备用方法）
        warning_threshold = self.min_distance * 1.5  # 警告距离阈值

        # 如果所有方向都安全，返回0度（直行）
        if np.min(distance_array) > warning_threshold:
            return 0.0

        # 计算每个方向的安全度
        safety_scores = np.zeros_like(distance_array)
        for i in range(len(distance_array)):
            # 安全度与距离成正比
            dist = distance_array[i]

            # 基础安全度
            if dist < self.min_distance * 0.5:
                # 极度危险区域内的安全度极低
                safety_scores[i] = dist / (self.min_distance * 0.5) * 0.1
            elif dist < self.min_distance:
                # 危险区域内的安全度很低
                safety_scores[i] = 0.1 + (dist - self.min_distance * 0.5) / (self.min_distance * 0.5) * 0.2
            elif dist < warning_threshold:
                # 警告区域内的安全度适中
                safety_scores[i] = 0.3 + (dist - self.min_distance) / (warning_threshold - self.min_distance) * 0.4
            else:
                # 安全区域内的安全度高
                safety_scores[i] = 0.7 + (dist - warning_threshold) / (100.0 - warning_threshold) * 0.3

            # 安全度与角度偏离成反比（优先选择正前方）
            angle = -90.0 + i * (180.0 / (len(distance_array) - 1))
            angle_penalty = abs(angle) / 90.0

            # 角度惩罚随距离变化
            # 距离越近，角度惩罚越小（允许更大的转向）
            if dist < self.min_distance * 0.5:
                angle_penalty *= 0.1  # 极度危险区域内，角度惩罚极小
            elif dist < self.min_distance:
                angle_penalty *= 0.2  # 危险区域内，角度惩罚很小
            elif dist < warning_threshold:
                angle_penalty *= 0.5  # 警告区域内，角度惩罚适中

            safety_scores[i] *= (1.0 - 0.5 * angle_penalty)

        # 找到最安全的方向
        safest_idx = np.argmax(safety_scores)
        safest_angle = -90.0 + safest_idx * (180.0 / (len(distance_array) - 1))

        # 限制转向角在[-max_angle, max_angle]范围内
        return np.clip(safest_angle, -self.max_angle, self.max_angle)

    def get_velocity_vector(self, observation: np.ndarray, current_height: float = 0.0) -> np.ndarray:
        """
        获取三维速度向量 (Vx, Vy, Vz)

        Args:
            observation: 深度图像或距离数组
            current_height: 当前高度

        Returns:
            三维速度向量 [Vx, Vy, Vz] (相对机体坐标系)
        """
        # 检查输入类型
        if len(observation.shape) == 2:
            # 输入是深度图像，需要传入高度信息
            result = self.process_depth_image(observation, current_height)
            distance_array = result["distance_array"]
            collision_risks = result["collision_risks"]
            min_distance = result["min_distance"]

            # 打印调试信息
            print(f"[3D避障调试] 最小障碍物距离: {min_distance:.2f}m")
            print(f"[3D避障调试] 当前高度: {current_height:.2f}m")
        else:
            # 输入是距离数组
            distance_array = observation
            min_distance = np.min(distance_array)
            collision_risks = self.last_collision_risks

        # 计算安全方向（转向角）
        if collision_risks is not None and len(collision_risks) > 0:
            turn_angle = find_safe_direction(collision_risks, self.current_yaw, self.max_angle)
        else:
            # 备用方法
            turn_angle = self._compute_safe_angle_from_distance(distance_array)

        # 将转向角转换为三维速度向量
        # 总速度大小始终为FORWARD_SPEED
        total_speed = FORWARD_SPEED

        # 将转向角转换为弧度
        turn_angle_rad = np.deg2rad(turn_angle)

        # 计算机体坐标系下的速度分量
        # Vx: 前向速度分量
        # Vy: 侧向速度分量
        # Vz: 垂直速度分量（暂时设为0，保持高度）
        vx = total_speed * np.cos(turn_angle_rad)  # 前向分量
        vy = total_speed * np.sin(turn_angle_rad)  # 侧向分量
        vz = 0.0  # 垂直分量，保持当前高度

        velocity_vector = np.array([vx, vy, vz])

        print(f"[3D避障调试] 转向角: {turn_angle:.2f}度")
        print(f"[3D避障调试] 速度向量: Vx={vx:.2f}, Vy={vy:.2f}, Vz={vz:.2f}")
        print(f"[3D避障调试] 总速度: {np.linalg.norm(velocity_vector[:2]):.2f}m/s")

        return velocity_vector

    def _compute_safe_angle_from_distance(self, distance_array: np.ndarray) -> float:
        """
        基于距离数组计算安全转向角（备用方法）

        Args:
            distance_array: 距离数组

        Returns:
            安全转向角（度）
        """
        warning_threshold = self.min_distance * 1.5

        # 如果所有方向都安全，返回0度（直行）
        if np.min(distance_array) > warning_threshold:
            return 0.0

        # 计算每个方向的安全度
        safety_scores = np.zeros_like(distance_array)
        for i in range(len(distance_array)):
            dist = distance_array[i]

            # 基础安全度
            if dist < self.min_distance * 0.5:
                safety_scores[i] = dist / (self.min_distance * 0.5) * 0.1
            elif dist < self.min_distance:
                safety_scores[i] = 0.1 + (dist - self.min_distance * 0.5) / (self.min_distance * 0.5) * 0.2
            elif dist < warning_threshold:
                safety_scores[i] = 0.3 + (dist - self.min_distance) / (warning_threshold - self.min_distance) * 0.4
            else:
                safety_scores[i] = 0.7 + (dist - warning_threshold) / (100.0 - warning_threshold) * 0.3

            # 角度惩罚
            angle = -90.0 + i * (180.0 / (len(distance_array) - 1))
            angle_penalty = abs(angle) / 90.0

            if dist < self.min_distance * 0.5:
                angle_penalty *= 0.1
            elif dist < self.min_distance:
                angle_penalty *= 0.2
            elif dist < warning_threshold:
                angle_penalty *= 0.5

            safety_scores[i] *= (1.0 - 0.5 * angle_penalty)

        # 找到最安全的方向
        safest_idx = np.argmax(safety_scores)
        safest_angle = -90.0 + safest_idx * (180.0 / (len(distance_array) - 1))

        return np.clip(safest_angle, -self.max_angle, self.max_angle)
